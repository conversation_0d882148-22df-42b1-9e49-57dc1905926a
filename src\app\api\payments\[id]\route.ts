import { NextRequest, NextResponse } from 'next/server'

import { supabase } from '@/lib/supabase'

// GET - Fetch single customer payment
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const { data: payment, error } = await supabase
      .from('customer_payments')
      .select('*')
      .eq('id', id)
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!payment) {
      return NextResponse.json({ error: 'Payment record not found' }, { status: 404 })
    }

    return NextResponse.json({ payment })
  } catch {
    return NextResponse.json(
      { error: 'Failed to fetch payment record' },
      { status: 500 }
    )
  }
}

// PUT - Update customer payment
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    const body = await request.json()
    const {
      customer_name,
      customer_family_name,
      payment_amount,
      payment_date,
      payment_method,
      responsible_family_member,
      notes
    } = body

    // Validate required fields
    if (!customer_name || !customer_family_name || !payment_amount) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      )
    }

    // Validate numeric fields
    if (typeof payment_amount !== 'number' || payment_amount <= 0) {
      return NextResponse.json(
        { error: 'Payment amount must be a positive number' },
        { status: 400 }
      )
    }

    // Validate string lengths
    if (customer_name.length < 2 || customer_name.length > 255) {
      return NextResponse.json(
        { error: 'Customer name must be between 2 and 255 characters' },
        { status: 400 }
      )
    }

    if (customer_family_name.length < 2 || customer_family_name.length > 255) {
      return NextResponse.json(
        { error: 'Customer family name must be between 2 and 255 characters' },
        { status: 400 }
      )
    }

    if (responsible_family_member && (responsible_family_member.length < 2 || responsible_family_member.length > 255)) {
      return NextResponse.json(
        { error: 'Responsible family member name must be between 2 and 255 characters' },
        { status: 400 }
      )
    }

    // Validate payment date if provided
    if (payment_date && isNaN(Date.parse(payment_date))) {
      return NextResponse.json(
        { error: 'Invalid payment date format' },
        { status: 400 }
      )
    }

    // Validate payment method
    const validPaymentMethods = ['Cash', 'GCash', 'PayMaya', 'Bank Transfer', 'Others']
    if (payment_method && !validPaymentMethods.includes(payment_method)) {
      return NextResponse.json(
        { error: 'Invalid payment method' },
        { status: 400 }
      )
    }

    const { data: payment, error } = await supabase
      .from('customer_payments')
      .update({
        customer_name: customer_name.trim(),
        customer_family_name: customer_family_name.trim(),
        payment_amount: Number(payment_amount),
        payment_date: payment_date || new Date().toISOString().split('T')[0],
        payment_method: payment_method || 'Cash',
        responsible_family_member: responsible_family_member?.trim() || null,
        notes: notes?.trim() || null
      })
      .eq('id', id)
      .select()
      .single()

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    if (!payment) {
      return NextResponse.json({ error: 'Payment record not found' }, { status: 404 })
    }

    return NextResponse.json({ payment })
  } catch {
    return NextResponse.json(
      { error: 'Failed to update payment record' },
      { status: 500 }
    )
  }
}

// DELETE - Delete customer payment
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params
    
    const { error } = await supabase
      .from('customer_payments')
      .delete()
      .eq('id', id)

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 })
    }

    return NextResponse.json({ message: 'Payment record deleted successfully' })
  } catch {
    return NextResponse.json(
      { error: 'Failed to delete payment record' },
      { status: 500 }
    )
  }
}
