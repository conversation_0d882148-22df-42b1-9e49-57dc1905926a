'use client'

import { CheckCircle, XCircle, AlertCircle, Info, X } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'

export interface ToastProps {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  onClose: (id: string) => void
}

export default function Toast({ id, type, title, message, duration = 5000, onClose }: ToastProps) {
  const { resolvedTheme } = useTheme()
  const [isVisible, setIsVisible] = useState(false)
  const [isExiting, setIsExiting] = useState(false)

  useEffect(() => {
    // Animate in
    setTimeout(() => setIsVisible(true), 100)

    // Auto close
    const timer = setTimeout(() => {
      handleClose()
    }, duration)

    return () => clearTimeout(timer)
  }, [duration])

  const handleClose = () => {
    setIsExiting(true)
    setTimeout(() => {
      onClose(id)
    }, 300)
  }

  const getIcon = () => {
    switch (type) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'warning':
        return <AlertCircle className="h-5 w-5 text-yellow-600" />
      case 'info':
        return <Info className="h-5 w-5 text-blue-600" />
    }
  }

  const getColors = () => {
    switch (type) {
      case 'success':
        return {
          bg: resolvedTheme === 'dark' ? '#064e3b' : '#f0fdf4',
          border: resolvedTheme === 'dark' ? '#059669' : '#22c55e',
          text: resolvedTheme === 'dark' ? '#d1fae5' : '#166534'
        }
      case 'error':
        return {
          bg: resolvedTheme === 'dark' ? '#7f1d1d' : '#fef2f2',
          border: resolvedTheme === 'dark' ? '#dc2626' : '#ef4444',
          text: resolvedTheme === 'dark' ? '#fecaca' : '#991b1b'
        }
      case 'warning':
        return {
          bg: resolvedTheme === 'dark' ? '#78350f' : '#fffbeb',
          border: resolvedTheme === 'dark' ? '#d97706' : '#f59e0b',
          text: resolvedTheme === 'dark' ? '#fed7aa' : '#92400e'
        }
      case 'info':
        return {
          bg: resolvedTheme === 'dark' ? '#1e3a8a' : '#eff6ff',
          border: resolvedTheme === 'dark' ? '#3b82f6' : '#60a5fa',
          text: resolvedTheme === 'dark' ? '#dbeafe' : '#1e40af'
        }
    }
  }

  const colors = getColors()

  return (
    <div
      className={`fixed top-4 right-4 z-50 max-w-sm w-full transform transition-all duration-300 ease-in-out ${
        isVisible && !isExiting
          ? 'translate-x-0 opacity-100 scale-100'
          : 'translate-x-full opacity-0 scale-95'
      }`}
    >
      <div
        className="rounded-lg shadow-lg border p-4"
        style={{
          backgroundColor: colors.bg,
          borderColor: colors.border,
          color: colors.text
        }}
      >
        <div className="flex items-start">
          <div className="flex-shrink-0">
            {getIcon()}
          </div>
          <div className="ml-3 flex-1">
            <h3 className="text-sm font-medium">{title}</h3>
            {message && (
              <p className="mt-1 text-sm opacity-90">{message}</p>
            )}
          </div>
          <div className="ml-4 flex-shrink-0">
            <button
              onClick={handleClose}
              className="inline-flex rounded-md p-1.5 hover:bg-black hover:bg-opacity-10 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

// Toast Container Component
interface ToastContainerProps {
  toasts: ToastProps[]
  onRemoveToast: (id: string) => void
}

export function ToastContainer({ toasts, onRemoveToast }: ToastContainerProps) {
  return (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map((toast, index) => (
        <div
          key={toast.id}
          style={{
            transform: `translateY(${index * 10}px)`,
            zIndex: 50 - index
          }}
        >
          <Toast {...toast} onClose={onRemoveToast} />
        </div>
      ))}
    </div>
  )
}

// Hook for managing toasts
export function useToast() {
  const [toasts, setToasts] = useState<ToastProps[]>([])

  const addToast = (toast: Omit<ToastProps, 'id' | 'onClose'>) => {
    const id = Math.random().toString(36).substr(2, 9)
    const newToast: ToastProps = {
      ...toast,
      id,
      onClose: removeToast
    }
    setToasts(prev => [...prev, newToast])
    return id
  }

  const removeToast = (id: string) => {
    setToasts(prev => prev.filter(toast => toast.id !== id))
  }

  const showSuccess = (title: string, message?: string) => {
    return addToast({ type: 'success', title, message: message || '' })
  }

  const showError = (title: string, message?: string) => {
    return addToast({ type: 'error', title, message: message || '' })
  }

  const showWarning = (title: string, message?: string) => {
    return addToast({ type: 'warning', title, message: message || '' })
  }

  const showInfo = (title: string, message?: string) => {
    return addToast({ type: 'info', title, message: message || '' })
  }

  return {
    toasts,
    addToast,
    removeToast,
    showSuccess,
    showError,
    showWarning,
    showInfo
  }
}
