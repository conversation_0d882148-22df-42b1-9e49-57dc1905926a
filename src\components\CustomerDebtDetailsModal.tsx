'use client'

import { X, Receipt, DollarSign, Calendar, User, Package, CreditCard, Users, FileText, Eye } from 'lucide-react'
import { useTheme } from 'next-themes'
import { useState, useEffect } from 'react'

import { CustomerDebt, CustomerPayment, Customer } from '@/lib/supabase'
import CustomerProfile from './CustomerProfile'

interface CustomerDebtDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  customerName: string
  customerFamilyName: string
}

export default function CustomerDebtDetailsModal({ 
  isOpen, 
  onClose, 
  customerName, 
  customerFamilyName 
}: CustomerDebtDetailsModalProps) {
  const { resolvedTheme } = useTheme()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [debts, setDebts] = useState<CustomerDebt[]>([])
  const [payments, setPayments] = useState<CustomerPayment[]>([])
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [activeTab, setActiveTab] = useState<'profile' | 'debts' | 'payments' | 'summary'>('profile')

  // Fetch customer debts and payments
  useEffect(() => {
    if (isOpen && customerName && customerFamilyName) {
      fetchCustomerData()
    }
  }, [isOpen, customerName, customerFamilyName])

  const fetchCustomerData = async () => {
    setLoading(true)
    setError(null)

    try {
      // Fetch customer profile
      const customerResponse = await fetch(
        `/api/customers?search=${encodeURIComponent(customerName + ' ' + customerFamilyName)}`
      )

      if (customerResponse.ok) {
        const customerData = await customerResponse.json()
        const foundCustomer = customerData.data?.customers?.find((c: Customer) =>
          c.customer_name === customerName && c.customer_family_name === customerFamilyName
        )
        setCustomer(foundCustomer || null)
      }

      // Fetch debts
      const debtsResponse = await fetch(
        `/api/debts?customer_name=${encodeURIComponent(customerName)}&customer_family_name=${encodeURIComponent(customerFamilyName)}`
      )

      if (!debtsResponse.ok) throw new Error('Failed to fetch debts')
      const debtsData = await debtsResponse.json()
      setDebts(debtsData.data?.debts || [])

      // Fetch payments
      const paymentsResponse = await fetch(
        `/api/payments?customer_name=${encodeURIComponent(customerName)}&customer_family_name=${encodeURIComponent(customerFamilyName)}`
      )

      if (!paymentsResponse.ok) throw new Error('Failed to fetch payments')
      const paymentsData = await paymentsResponse.json()
      setPayments(paymentsData.data?.payments || [])

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch customer data')
    } finally {
      setLoading(false)
    }
  }

  // Handle customer profile update
  const handleUpdateCustomer = async (updatedData: Partial<Customer>) => {
    if (!customer) return

    try {
      const response = await fetch(`/api/customers/${customer.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updatedData),
      })

      if (!response.ok) {
        throw new Error('Failed to update customer')
      }

      const result = await response.json()
      setCustomer(result.customer)
    } catch (error) {
      console.error('Error updating customer:', error)
      throw error
    }
  }

  // Handle creating customer profile
  const handleCreateCustomerProfile = async () => {
    try {
      console.log('Creating customer profile for:', customerName, customerFamilyName)

      const requestBody = {
        customer_name: customerName,
        customer_family_name: customerFamilyName,
      }

      console.log('Request body:', requestBody)

      const response = await fetch('/api/customers', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
      })

      console.log('Response status:', response.status)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        console.error('API Error:', errorData)
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to create customer profile`)
      }

      const result = await response.json()
      console.log('Customer created successfully:', result)
      setCustomer(result.customer)

      // Show success message
      alert('Customer profile created successfully!')
    } catch (error) {
      console.error('Error creating customer profile:', error)
      const errorMessage = error instanceof Error ? error.message : 'Failed to create customer profile'
      alert(`Error: ${errorMessage}`)
    }
  }

  // Calculate summary
  const summary = {
    totalDebt: debts.reduce((sum, debt) => sum + debt.total_amount, 0),
    totalPayments: payments.reduce((sum, payment) => sum + payment.payment_amount, 0),
    debtCount: debts.length,
    paymentCount: payments.length
  }
  summary.remainingBalance = summary.totalDebt - summary.totalPayments

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
      <div
        className="w-full max-w-4xl max-h-[90vh] overflow-hidden rounded-2xl shadow-2xl"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b" style={{
          borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
        }}>
          <div>
            <h2
              className="text-xl font-semibold"
              style={{
                color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
              }}
            >
              Customer Debt Details
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {customerName} {customerFamilyName}
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-lg text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="flex border-b overflow-x-auto" style={{
          borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
        }}>
          {[
            { id: 'profile', label: 'Profile', icon: User },
            { id: 'summary', label: 'Summary', icon: Eye },
            { id: 'debts', label: `Debts (${summary.debtCount})`, icon: Receipt },
            { id: 'payments', label: `Payments (${summary.paymentCount})`, icon: DollarSign }
          ].map(({ id, label, icon: Icon }) => (
            <button
              key={id}
              onClick={() => setActiveTab(id as any)}
              className={`flex items-center px-6 py-3 text-sm font-medium transition-colors whitespace-nowrap ${
                activeTab === id
                  ? 'text-green-600 border-b-2 border-green-600'
                  : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200'
              }`}
            >
              <Icon className="h-4 w-4 mr-2" />
              {label}
            </button>
          ))}
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {loading ? (
            <div className="flex items-center justify-center py-12">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"></div>
              <span className="ml-3 text-gray-600 dark:text-gray-400">Loading customer data...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-500 mb-2">Error loading data</div>
              <div className="text-sm text-gray-600 dark:text-gray-400">{error}</div>
            </div>
          ) : (
            <>
              {/* Profile Tab */}
              {activeTab === 'profile' && (
                <div className="space-y-6">
                  {customer ? (
                    <CustomerProfile
                      customer={customer}
                      onUpdate={handleUpdateCustomer}
                      isEditable={true}
                    />
                  ) : (
                    <div className="text-center py-12">
                      <User className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 dark:text-gray-400">
                        Customer profile not found. This customer may need to be added to the customer database.
                      </p>
                      <button
                        onClick={handleCreateCustomerProfile}
                        className="mt-4 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        Create Customer Profile
                      </button>
                    </div>
                  )}
                </div>
              )}

              {/* Summary Tab */}
              {activeTab === 'summary' && (
                <div className="space-y-6">
                  {/* Summary Cards */}
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <div className="p-4 rounded-lg border" style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                    }}>
                      <div className="flex items-center">
                        <Receipt className="h-8 w-8 text-red-500" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Debt</p>
                          <p
                            className="text-lg font-semibold"
                            style={{
                              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                            }}
                          >
                            ₱{summary.totalDebt.toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 rounded-lg border" style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                    }}>
                      <div className="flex items-center">
                        <DollarSign className="h-8 w-8 text-green-500" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Payments</p>
                          <p
                            className="text-lg font-semibold"
                            style={{
                              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                            }}
                          >
                            ₱{summary.totalPayments.toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 rounded-lg border" style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                    }}>
                      <div className="flex items-center">
                        <CreditCard className={`h-8 w-8 ${summary.remainingBalance > 0 ? 'text-orange-500' : 'text-green-500'}`} />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Remaining Balance</p>
                          <p className={`text-lg font-semibold ${summary.remainingBalance > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                            ₱{summary.remainingBalance.toFixed(2)}
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="p-4 rounded-lg border" style={{
                      backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                      borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                    }}>
                      <div className="flex items-center">
                        <Users className="h-8 w-8 text-blue-500" />
                        <div className="ml-3">
                          <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Transactions</p>
                          <p
                            className="text-lg font-semibold"
                            style={{
                              color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                            }}
                          >
                            {summary.debtCount + summary.paymentCount}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Progress */}
                  <div className="p-4 rounded-lg border" style={{
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                    borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                  }}>
                    <h3
                      className="text-lg font-semibold mb-3"
                      style={{
                        color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                      }}
                    >
                      Payment Progress
                    </h3>
                    <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                      <div 
                        className="bg-green-600 h-3 rounded-full transition-all duration-300"
                        style={{ 
                          width: `${summary.totalDebt > 0 ? Math.min((summary.totalPayments / summary.totalDebt) * 100, 100) : 0}%` 
                        }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600 dark:text-gray-400 mt-2">
                      <span>
                        {summary.totalDebt > 0 ? ((summary.totalPayments / summary.totalDebt) * 100).toFixed(1) : 0}% paid
                      </span>
                      <span>
                        {summary.remainingBalance > 0 ? `₱${summary.remainingBalance.toFixed(2)} remaining` : 'Fully paid'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Debts Tab */}
              {activeTab === 'debts' && (
                <div className="space-y-4">
                  <h3
                    className="text-lg font-semibold"
                    style={{
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                    }}
                  >
                    Debt Records ({debts.length})
                  </h3>

                  {debts.length === 0 ? (
                    <div className="text-center py-8">
                      <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 dark:text-gray-400">No debt records found</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {debts.map((debt) => (
                        <div
                          key={debt.id}
                          className="p-4 rounded-lg border"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                            borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                          }}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center mb-2">
                                <Package className="h-4 w-4 text-blue-500 mr-2" />
                                <h4
                                  className="font-semibold"
                                  style={{
                                    color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                  }}
                                >
                                  {debt.product_name}
                                </h4>
                              </div>

                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Price:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    ₱{debt.product_price.toFixed(2)}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Quantity:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {debt.quantity}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Total:</span>
                                  <p className="font-semibold text-red-600">₱{debt.total_amount.toFixed(2)}</p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Date:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {new Date(debt.debt_date).toLocaleDateString()}
                                  </p>
                                </div>
                              </div>

                              {debt.notes && (
                                <div className="mt-3 flex items-start">
                                  <FileText className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                                  <p className="text-sm text-gray-600 dark:text-gray-400">{debt.notes}</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}

              {/* Payments Tab */}
              {activeTab === 'payments' && (
                <div className="space-y-4">
                  <h3
                    className="text-lg font-semibold"
                    style={{
                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                    }}
                  >
                    Payment Records ({payments.length})
                  </h3>

                  {payments.length === 0 ? (
                    <div className="text-center py-8">
                      <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-3" />
                      <p className="text-gray-600 dark:text-gray-400">No payment records found</p>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      {payments.map((payment) => (
                        <div
                          key={payment.id}
                          className="p-4 rounded-lg border"
                          style={{
                            backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb',
                            borderColor: resolvedTheme === 'dark' ? '#4b5563' : '#e5e7eb'
                          }}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-center mb-2">
                                <DollarSign className="h-4 w-4 text-green-500 mr-2" />
                                <h4 className="font-semibold text-green-600">
                                  ₱{payment.payment_amount.toFixed(2)}
                                </h4>
                              </div>

                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Date:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {new Date(payment.payment_date).toLocaleDateString()}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Method:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {payment.payment_method}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Paid by:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {payment.responsible_family_member || `${customerName} ${customerFamilyName}`}
                                  </p>
                                </div>
                                <div>
                                  <span className="text-gray-600 dark:text-gray-400">Time:</span>
                                  <p
                                    className="font-medium"
                                    style={{
                                      color: resolvedTheme === 'dark' ? '#ffffff' : '#111827'
                                    }}
                                  >
                                    {new Date(payment.created_at).toLocaleTimeString()}
                                  </p>
                                </div>
                              </div>

                              {payment.notes && (
                                <div className="mt-3 flex items-start">
                                  <FileText className="h-4 w-4 text-gray-500 mr-2 mt-0.5" />
                                  <p className="text-sm text-gray-600 dark:text-gray-400">{payment.notes}</p>
                                </div>
                              )}

                              {payment.responsible_family_member && (
                                <div className="mt-2 flex items-center">
                                  <Users className="h-4 w-4 text-blue-500 mr-2" />
                                  <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">
                                    Family member payment
                                  </span>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  )
}
