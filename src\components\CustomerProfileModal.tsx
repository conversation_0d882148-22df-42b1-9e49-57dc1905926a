'use client'

import { X, User, Camera, Phone, MapPin, FileText, Trash2 } from 'lucide-react'
import Image from 'next/image'
import { useTheme } from 'next-themes'
import { useState, useEffect, useRef } from 'react'

import { Customer } from '@/lib/supabase'

interface CustomerProfileModalProps {
  isOpen: boolean
  onClose: () => void
  customer?: Customer | null
  customerName?: string
  customerFamilyName?: string
  onProfileUpdated?: (customer: Customer) => void
}

export default function CustomerProfileModal({
  isOpen,
  onClose,
  customer,
  customerName = '',
  customerFamilyName = '',
  onProfileUpdated
}: CustomerProfileModalProps) {
  const { resolvedTheme } = useTheme()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [formData, setFormData] = useState({
    customer_name: '',
    customer_family_name: '',
    profile_picture_url: '',
    profile_picture_public_id: '',
    phone_number: '',
    address: '',
    notes: ''
  })

  useEffect(() => {
    if (isOpen) {
      if (customer) {
        // Editing existing customer
        setFormData({
          customer_name: customer.customer_name,
          customer_family_name: customer.customer_family_name,
          profile_picture_url: customer.profile_picture_url || '',
          profile_picture_public_id: customer.profile_picture_public_id || '',
          phone_number: customer.phone_number || '',
          address: customer.address || '',
          notes: customer.notes || ''
        })
      } else {
        // New customer with pre-filled names
        setFormData({
          customer_name: customerName,
          customer_family_name: customerFamilyName,
          profile_picture_url: '',
          profile_picture_public_id: '',
          phone_number: '',
          address: '',
          notes: ''
        })
      }
    }
  }, [isOpen, customer, customerName, customerFamilyName])

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if (!allowedTypes.includes(file.type)) {
      alert('Please select a valid image file (JPEG, PNG, or WebP)')
      return
    }

    // Validate file size (5MB max)
    const maxSize = 5 * 1024 * 1024
    if (file.size > maxSize) {
      alert('File size must be less than 5MB')
      return
    }

    setUploading(true)
    try {
      const uploadFormData = new FormData()
      uploadFormData.append('file', file)

      const response = await fetch('/api/upload/profile-picture', {
        method: 'POST',
        body: uploadFormData,
      })

      if (response.ok) {
        const data = await response.json()
        setFormData(prev => ({
          ...prev,
          profile_picture_url: data.url,
          profile_picture_public_id: data.public_id
        }))
      } else {
        const errorData = await response.json()
        alert(`Error uploading image: ${errorData.error || 'Unknown error'}`)
      }
    } catch (error) {
      console.error('Error uploading image:', error)
      alert('Error uploading image. Please check your connection and try again.')
    } finally {
      setUploading(false)
      // Clear the input so the same file can be selected again if needed
      event.target.value = ''
    }
  }

  const handleRemoveImage = async () => {
    // If there's a Cloudinary public_id, delete from Cloudinary
    if (formData.profile_picture_public_id) {
      try {
        await fetch(`/api/upload/profile-picture?public_id=${encodeURIComponent(formData.profile_picture_public_id)}`, {
          method: 'DELETE'
        })
      } catch (error) {
        console.error('Error deleting image from Cloudinary:', error)
      }
    }

    setFormData(prev => ({
      ...prev,
      profile_picture_url: '',
      profile_picture_public_id: ''
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      const url = customer ? `/api/customers/${customer.id}` : '/api/customers'
      const method = customer ? 'PUT' : 'POST'

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      })

      if (response.ok) {
        const result = await response.json()

        // Call the callback to notify parent component of the update
        if (onProfileUpdated && result.customer) {
          onProfileUpdated(result.customer)
        }

        // Profile saved successfully
        onClose()
      } else {
        const errorData = await response.json()
        console.error('Error saving customer profile:', errorData.error || 'Unknown error')
        alert('Error saving customer profile. Please try again.')
      }
    } catch (error) {
      console.error('Error saving customer profile:', error)
      alert('Error saving customer profile. Please check your connection and try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div
        className="w-full max-w-lg rounded-xl shadow-2xl max-h-[90vh] overflow-y-auto"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff'
        }}
      >
        {/* Header */}
        <div
          className="flex justify-between items-center p-6 border-b"
          style={{
            borderColor: resolvedTheme === 'dark' ? '#334155' : '#e5e7eb'
          }}
        >
          <div className="flex items-center gap-3">
            <div className="p-2 rounded-lg bg-blue-100">
              <User className="h-5 w-5 text-blue-600" />
            </div>
            <div>
              <h2
                className="text-xl font-semibold"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                {customer ? 'Edit Customer Profile' : 'Create Customer Profile'}
              </h2>
              <p
                className="text-sm"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                Manage customer information and profile picture
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
          >
            <X className="h-5 w-5" style={{
              color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
            }} />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-6">
          {/* Profile Picture Section */}
          <div className="flex flex-col items-center space-y-4">
            <div className="relative">
              {formData.profile_picture_url ? (
                <div className="relative">
                  <div className="w-32 h-32 rounded-full overflow-hidden border-4 border-white shadow-lg">
                    <Image
                      src={formData.profile_picture_url}
                      alt="Profile"
                      width={128}
                      height={128}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={handleRemoveImage}
                    className="absolute -top-2 -right-2 p-1.5 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-lg"
                  >
                    <Trash2 className="h-3 w-3" />
                  </button>
                </div>
              ) : (
                <div
                  className="w-32 h-32 rounded-full border-2 border-dashed flex items-center justify-center"
                  style={{
                    borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
                    backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb'
                  }}
                >
                  <User className="h-12 w-12" style={{
                    color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
                  }} />
                </div>
              )}
              
              <button
                type="button"
                onClick={() => fileInputRef.current?.click()}
                disabled={uploading}
                className="absolute -bottom-2 -right-2 p-2 bg-blue-500 text-white rounded-full hover:bg-blue-600 transition-colors shadow-lg disabled:opacity-50"
              >
                {uploading ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                ) : (
                  <Camera className="h-4 w-4" />
                )}
              </button>
            </div>

            <input
              ref={fileInputRef}
              type="file"
              accept="image/*"
              onChange={handleImageUpload}
              className="hidden"
            />

            <p
              className="text-sm text-center"
              style={{
                color: resolvedTheme === 'dark' ? '#9ca3af' : '#6b7280'
              }}
            >
              Click the camera icon to upload a profile picture
            </p>
          </div>

          {/* Customer Name Fields */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                First Name *
              </label>
              <input
                type="text"
                value={formData.customer_name}
                onChange={(e) => setFormData({ ...formData, customer_name: e.target.value })}
                className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
                required
              />
            </div>
            <div>
              <label
                className="block text-sm font-medium mb-2"
                style={{
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
              >
                Last Name *
              </label>
              <input
                type="text"
                value={formData.customer_family_name}
                onChange={(e) => setFormData({ ...formData, customer_family_name: e.target.value })}
                className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                style={{
                  backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                  border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                  color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
                }}
                required
              />
            </div>
          </div>

          {/* Phone Number */}
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            >
              <Phone className="h-4 w-4 inline mr-1" />
              Phone Number
            </label>
            <input
              type="tel"
              value={formData.phone_number}
              onChange={(e) => setFormData({ ...formData, phone_number: e.target.value })}
              className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="09XX XXX XXXX"
            />
          </div>

          {/* Address */}
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            >
              <MapPin className="h-4 w-4 inline mr-1" />
              Address
            </label>
            <textarea
              value={formData.address}
              onChange={(e) => setFormData({ ...formData, address: e.target.value })}
              rows={2}
              className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="Customer's address..."
            />
          </div>

          {/* Notes */}
          <div>
            <label
              className="block text-sm font-medium mb-2"
              style={{
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            >
              <FileText className="h-4 w-4 inline mr-1" />
              Notes
            </label>
            <textarea
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300 resize-none"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #6b7280' : '1px solid #d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
              placeholder="Additional notes about the customer..."
            />
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border rounded-lg font-medium transition-all duration-300 hover:bg-gray-50 dark:hover:bg-gray-700"
              style={{
                borderColor: resolvedTheme === 'dark' ? '#6b7280' : '#d1d5db',
                color: resolvedTheme === 'dark' ? '#f9fafb' : '#111827'
              }}
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={loading}
              className="flex-1 px-4 py-2 bg-gradient-to-r from-blue-600 to-indigo-600 text-white rounded-lg font-medium hover:from-blue-700 hover:to-indigo-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? 'Saving...' : customer ? 'Update Profile' : 'Create Profile'}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}
