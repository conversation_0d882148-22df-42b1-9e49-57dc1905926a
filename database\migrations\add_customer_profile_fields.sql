-- Migration: Add customer profile fields
-- Date: 2025-07-28
-- Description: Add birth_date and birth_place columns to customers table

-- Add new columns to customers table
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS birth_date DATE,
ADD COLUMN IF NOT EXISTS birth_place VARCHAR(255);

-- Add constraints for the new columns
ALTER TABLE customers 
ADD CONSTRAINT IF NOT EXISTS customers_birth_date_valid 
CHECK (birth_date IS NULL OR birth_date <= CURRENT_DATE);

ALTER TABLE customers 
ADD CONSTRAINT IF NOT EXISTS customers_birth_place_not_empty 
CHECK (birth_place IS NULL OR LENGTH(TRIM(birth_place)) > 0);

-- Update the updated_at trigger to include new columns
-- (This ensures the updated_at timestamp is updated when profile fields change)

-- Create or replace the trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Drop existing trigger if it exists and recreate
DROP TRIGGER IF EXISTS update_customers_updated_at ON customers;
CREATE TRIGGER update_customers_updated_at
    BEFORE UPDATE ON customers
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON COLUMN customers.birth_date IS 'Customer birth date for profile information';
COMMENT ON COLUMN customers.birth_place IS 'Customer birthplace for profile information';
