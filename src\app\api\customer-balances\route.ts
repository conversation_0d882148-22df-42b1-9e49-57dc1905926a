import { NextRequest } from 'next/server'

import {
  successResponse,
  with<PERSON><PERSON>r<PERSON><PERSON><PERSON>,
  handleDatabaseError,
  parsePaginationParams,
  handleCorsPreflightRequest
} from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

// Handle CORS preflight requests
export async function OPTIONS() {
  return handleCorsPreflightRequest()
}

// GET - Fetch customer balances with pagination and filtering
export const GET = withErrorHandler(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url)
  const { page, limit, offset } = parsePaginationParams(searchParams)

  // Optional filters
  const search = searchParams.get('search')
  const hasBalance = searchParams.get('has_balance') // 'true' to show only customers with remaining balance

  let query = supabase
    .from('customer_balances')
    .select('*', { count: 'exact' })
    .order('remaining_balance', { ascending: false })
    .range(offset, offset + limit - 1)

  // Apply search filter
  if (search) {
    query = query.or(`customer_name.ilike.%${search}%,customer_family_name.ilike.%${search}%`)
  }

  // Filter customers with remaining balance
  if (hasBalance === 'true') {
    query = query.gt('remaining_balance', 0)
  }

  const { data: balances, error, count } = await query

  if (error) {
    return handleDatabaseError(error)
  }

  return successResponse({
    balances,
    pagination: {
      page,
      limit,
      total: count || 0,
      totalPages: Math.ceil((count || 0) / limit)
    }
  })
})
