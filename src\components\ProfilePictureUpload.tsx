'use client'

import { useState, useRef } from 'react'
import { Camera, Upload, User, X, Check } from 'lucide-react'
import { useTheme } from 'next-themes'
import Image from 'next/image'

interface ProfilePictureUploadProps {
  currentImageUrl?: string
  onImageChange: (imageUrl: string | null, publicId: string | null) => void
  size?: 'sm' | 'md' | 'lg' | 'xl'
  disabled?: boolean
  className?: string
}

const sizeClasses = {
  sm: 'w-16 h-16',
  md: 'w-24 h-24', 
  lg: 'w-32 h-32',
  xl: 'w-40 h-40'
}

const iconSizes = {
  sm: 'h-6 w-6',
  md: 'h-8 w-8',
  lg: 'h-12 w-12', 
  xl: 'h-16 w-16'
}

export default function ProfilePictureUpload({
  currentImageUrl,
  onImageChange,
  size = 'lg',
  disabled = false,
  className = ''
}: ProfilePictureUploadProps) {
  const { resolvedTheme } = useTheme()
  const [isUploading, setIsUploading] = useState(false)
  const [dragOver, setDragOver] = useState(false)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (file: File) => {
    if (!file || disabled) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert('Please select an image file')
      return
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      alert('Image size must be less than 5MB')
      return
    }

    setIsUploading(true)

    try {
      // Create FormData for upload
      const formData = new FormData()
      formData.append('file', file)
      formData.append('upload_preset', 'customer_profiles') // You'll need to set this in Cloudinary
      formData.append('folder', 'tindahan/customers')

      // Upload to Cloudinary
      const response = await fetch(
        `https://api.cloudinary.com/v1_1/${process.env.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME}/image/upload`,
        {
          method: 'POST',
          body: formData,
        }
      )

      if (!response.ok) {
        throw new Error('Upload failed')
      }

      const data = await response.json()
      onImageChange(data.secure_url, data.public_id)
    } catch (error) {
      console.error('Upload error:', error)
      alert('Failed to upload image. Please try again.')
    } finally {
      setIsUploading(false)
    }
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
    
    const files = Array.from(e.dataTransfer.files)
    if (files.length > 0) {
      handleFileSelect(files[0])
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setDragOver(false)
  }

  const handleClick = () => {
    if (!disabled && fileInputRef.current) {
      fileInputRef.current.click()
    }
  }

  const handleRemove = (e: React.MouseEvent) => {
    e.stopPropagation()
    onImageChange(null, null)
  }

  return (
    <div className={`relative ${className}`}>
      <div
        className={`
          ${sizeClasses[size]} 
          relative rounded-full overflow-hidden border-2 cursor-pointer transition-all duration-200
          ${dragOver ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-300 dark:border-gray-600'}
          ${disabled ? 'cursor-not-allowed opacity-50' : 'hover:border-blue-400 hover:shadow-lg'}
          ${isUploading ? 'animate-pulse' : ''}
        `}
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#374151' : '#f9fafb'
        }}
        onClick={handleClick}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        {currentImageUrl ? (
          <>
            <Image
              src={currentImageUrl}
              alt="Profile"
              fill
              className="object-cover"
              sizes={`${sizeClasses[size].split(' ')[0].replace('w-', '')}px`}
            />
            {!disabled && (
              <div className="absolute inset-0 bg-black bg-opacity-0 hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center">
                <Camera className="h-6 w-6 text-white opacity-0 hover:opacity-100 transition-opacity duration-200" />
              </div>
            )}
            {!disabled && (
              <button
                onClick={handleRemove}
                className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors shadow-lg"
                title="Remove photo"
              >
                <X className="h-3 w-3" />
              </button>
            )}
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-full text-gray-500 dark:text-gray-400">
            {isUploading ? (
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            ) : (
              <>
                <User className={iconSizes[size]} />
                {size !== 'sm' && (
                  <div className="mt-2 text-center">
                    <Upload className="h-4 w-4 mx-auto mb-1" />
                    <p className="text-xs">Upload Photo</p>
                  </div>
                )}
              </>
            )}
          </div>
        )}
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={(e) => {
          const file = e.target.files?.[0]
          if (file) handleFileSelect(file)
        }}
        className="hidden"
        disabled={disabled}
      />

      {size !== 'sm' && (
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-2 text-center">
          Click or drag to upload
          <br />
          Max 5MB, JPG/PNG
        </p>
      )}
    </div>
  )
}
